package cn.com.vau.trade.ext

import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.trade.bean.SymbolItemBean
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_DOWN
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_NONE
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_UP
import cn.com.vau.util.formatProductPrice
import cn.com.vau.util.ifNull
import cn.com.vau.util.numFormat
import cn.com.vau.util.toFloatCatching
import kotlin.math.abs

object SymbolSearchUtil {

    /**
     * 按当前排序规则排序
     * 0、SORT_NONE，不排序
     * 1、SORT_DOWN，由高到低排序
     * 2、SORT_UP，由低到高排序
     */
    fun sort(sortMode: Int, dataList: ArrayList<SymbolItemBean>, sortedCallback: (() -> Unit)? = null) {
        when (sortMode) {
            SORT_DOWN -> dataList.sortWith { product1, product2 ->
                val o1 = product1.product
                val o2 = product2.product
                val bidUI1 = o1.bidUI.ifEmpty { o1.bid.formatProductPrice(o1.digits, true, Constants.DOUBLE_LINE) }
                val bidUI2 = o2.bidUI.ifEmpty { o2.bid.formatProductPrice(o2.digits, true, Constants.DOUBLE_LINE) }
                val roseUI1 = o1.roseUI.ifEmpty { if (o1.closePrice.toFloatCatching() == 0f || bidUI1 == Constants.DOUBLE_LINE) Constants.DOUBLE_LINE else o1.rose.numFormat(2, true) }
                val roseUI2 = o2.roseUI.ifEmpty { if (o2.closePrice.toFloatCatching() == 0f || bidUI2 == Constants.DOUBLE_LINE) Constants.DOUBLE_LINE else o2.rose.numFormat(2, true) }

                // 修复比较器契约问题：确保传递性和一致性
                val isInvalid1 = roseUI1 == "--"
                val isInvalid2 = roseUI2 == "--"

                when {
                    isInvalid1 && isInvalid2 -> {
                        // 当两个都是无效值时，使用symbol进行稳定排序，确保一致性
                        o1.symbol.compareTo(o2.symbol)
                    }
                    isInvalid1 -> 1  // 无效值排在后面
                    isInvalid2 -> -1 // 无效值排在后面
                    else -> {
                        // 按涨幅从高到低排序 (o2 - o1)
                        val rose1 = o1.rose.ifNull()
                        val rose2 = o2.rose.ifNull()
                        val result = rose2.compareTo(rose1)
                        // 如果涨幅相同，使用symbol确保稳定排序
                        if (result == 0) o1.symbol.compareTo(o2.symbol) else result
                    }
                }
            }

            SORT_UP -> dataList.sortWith { product1, product2 ->
                val o1 = product1.product
                val o2 = product2.product
                val bidUI1 = o1.bidUI.ifEmpty { o1.bid.formatProductPrice(o1.digits, true, Constants.DOUBLE_LINE) }
                val bidUI2 = o2.bidUI.ifEmpty { o2.bid.formatProductPrice(o2.digits, true, Constants.DOUBLE_LINE) }
                val roseUI1 = o1.roseUI.ifEmpty { if (o1.closePrice.toFloatCatching() == 0f || bidUI1 == Constants.DOUBLE_LINE) Constants.DOUBLE_LINE else o1.rose.numFormat(2, true) }
                val roseUI2 = o2.roseUI.ifEmpty { if (o2.closePrice.toFloatCatching() == 0f || bidUI2 == Constants.DOUBLE_LINE) Constants.DOUBLE_LINE else o2.rose.numFormat(2, true) }

                // 修复比较器契约问题：确保传递性和一致性
                val isInvalid1 = roseUI1 == "--"
                val isInvalid2 = roseUI2 == "--"

                when {
                    isInvalid1 && isInvalid2 -> {
                        // 当两个都是无效值时，使用symbol进行稳定排序，确保一致性
                        o1.symbol.compareTo(o2.symbol)
                    }
                    isInvalid1 -> 1  // 无效值排在后面
                    isInvalid2 -> -1 // 无效值排在后面
                    else -> {
                        // 按涨幅从低到高排序 (o1 - o2)
                        val rose1 = o1.rose.ifNull()
                        val rose2 = o2.rose.ifNull()
                        val result = rose1.compareTo(rose2)
                        // 如果涨幅相同，使用symbol确保稳定排序
                        if (result == 0) o1.symbol.compareTo(o2.symbol) else result
                    }
                }
            }

            else -> {}
        }

        sortedCallback?.invoke()
    }

    /**
     * 下一个排序模式
     * 0、不排序
     * 1、由高到低排序
     * 2、由低到高排序
     */
    fun nextSort(curSortMode: Int, nextBlock: ((sortMode: Int) -> Unit)? = null) {
        var sortMode = curSortMode    // 0 -> 1 -> 2 -> 0
        when (sortMode) {
            SORT_NONE -> {
                sortMode = SORT_DOWN
                nextBlock?.invoke(sortMode)
            }

            SORT_DOWN -> {
                sortMode = SORT_UP
                nextBlock?.invoke(sortMode)
            }

            SORT_UP -> {
                sortMode = SORT_NONE
                nextBlock?.invoke(sortMode)
            }
        }
    }
}

object SymbolSearchConstants {
    const val SS_CATEGORY_SYMBOL_GROUP_NAME = "SS_CATEGORY_SYMBOL_GROUP_NAME" //分类页产品列表分类名字
    const val SS_SEARCH_SYMBOL_GROUP_NAME = "SS_SEARCH_SYMBOL_GROUP_NAME" //搜索页产品列表分类名字
    const val TAG_SS_CATEGORY_FRAGMENT = "TAG_SS_CATEGORY_FRAGMENT"
    const val TAG_SS_SEARCH_FRAGMENT = "TAG_SS_SEARCH_FRAGMENT"
    const val SEARCH_ALL_INDEX = 0 //全部分类index
    const val FROM_K_LINE = 0 //来自K线页
    const val FROM_ORDER = 1 //来自下单页
    const val FROM_TRADE_ORDER = 2 //来自订单合并页面
    const val SORT_NONE: Int = 0 // 不排序
    const val SORT_DOWN: Int = 1// 由高到低排序
    const val SORT_UP: Int = 2 // 由低到高排序

}

/**
 * RecyclerView滚动监听
 */
fun RecyclerView.addScrollListener(
    scrollThreshold: Int = 20,
    onScroll: ((isScrolling: Boolean, scrollY: Int, direction: Int) -> Unit)? = null
) {
    val scrollListener = object : RecyclerView.OnScrollListener() {
        private var lastScrollY = 0
        private var isScrolling = false

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            when (newState) {
                RecyclerView.SCROLL_STATE_DRAGGING -> {
                    isScrolling = true
                    lastScrollY = computeVerticalScrollOffset()
                }

                RecyclerView.SCROLL_STATE_SETTLING -> {
                    isScrolling = true
                }

                RecyclerView.SCROLL_STATE_IDLE -> {
                    isScrolling = false
                }
            }
            onScroll?.invoke(isScrolling, computeVerticalScrollOffset(), 0)
        }

        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            if (isScrolling) {
                val currentScrollY = computeVerticalScrollOffset()
                if (abs(currentScrollY - lastScrollY) >= scrollThreshold) {
                    val direction = if (currentScrollY > lastScrollY) 1 else -1
                    onScroll?.invoke(true, currentScrollY, direction)
                    lastScrollY = currentScrollY
                }
            }
        }
    }

    addOnScrollListener(scrollListener)
}

/**
 * 添加ViewPager2滚动状态监听器
 * @param onScrollStateChanged 滚动状态回调 (isScrolling: Boolean)
 * @param scrollThreshold 滚动阈值（像素），默认10像素
 */
fun ViewPager2.addScrollStateListener(
    scrollThreshold: Int = 10,
    onScrollStateChanged: (Boolean) -> Unit,
) {
    var lastScrollX = 0
    var isCurrentlyScrolling = false

    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
            val scrollDistance = abs(positionOffsetPixels - lastScrollX)
            lastScrollX = positionOffsetPixels

            if (scrollDistance > scrollThreshold && !isCurrentlyScrolling) {
                isCurrentlyScrolling = true
                onScrollStateChanged(true)
            }
        }

        override fun onPageScrollStateChanged(state: Int) {
            val newScrollingState = when (state) {
                ViewPager2.SCROLL_STATE_DRAGGING,
                ViewPager2.SCROLL_STATE_SETTLING -> true

                ViewPager2.SCROLL_STATE_IDLE -> false
                else -> isCurrentlyScrolling
            }

            if (newScrollingState != isCurrentlyScrolling) {
                isCurrentlyScrolling = newScrollingState
                onScrollStateChanged(newScrollingState)
            }
        }
    })
}

/**
 * 复制一个新集合
 */
fun ArrayList<SymbolItemBean>.deepCopy(): ArrayList<SymbolItemBean> {
    return this.mapTo(ArrayList(this.size)) { originalItem ->
        originalItem.deepCopy()
    }
}

/**
 * 复制一个新产品对象
 */
fun ShareProductData.copyData(): ShareProductData {
    return ShareProductData().apply {
        this.refresh = <EMAIL>
        this.originalBid = <EMAIL>
        this.originalAsk = <EMAIL>
        this.bidType = <EMAIL>
        this.askType = <EMAIL>
        this.closePrice = <EMAIL>
        this.marketClose = <EMAIL>
        this.isOptionSelected = <EMAIL>
        this.rose = <EMAIL>
        this.diff = <EMAIL>
        this.lasttime = <EMAIL>
        this.bidUI = <EMAIL>
        this.askUI = <EMAIL>
        this.roseUI = <EMAIL>
        this.spreadUI = <EMAIL>
        this.diffUI = <EMAIL>
        this.symbol = <EMAIL>
        this.bid = <EMAIL>
        this.ask = <EMAIL>
        this.multiLang = <EMAIL>?.toList()
        this.img_url = this@copyData.img_url
        this.description = <EMAIL>
        this.pips = <EMAIL>
        this.contractsize = <EMAIL>
        this.digits = <EMAIL>
        this.enable = <EMAIL>
        this.leverage = <EMAIL>
        this.currency = <EMAIL>
        this.priceCurrency = <EMAIL>
        this.margin_currency = this@copyData.margin_currency
        this.profit_currency = this@copyData.profit_currency
        this.margininit = <EMAIL>
        this.marginmodel = <EMAIL>
        this.marginpercent = <EMAIL>
        this.maxprice = <EMAIL>
        this.maxvolume = <EMAIL>
        this.minprice = <EMAIL>
        this.minvolume = <EMAIL>
        this.open = <EMAIL>
        this.stepvolume = <EMAIL>
        this.stoplossmodel = <EMAIL>
        this.stopslevel = <EMAIL>
        this.tradetime = ArrayList(<EMAIL>)
        this.tickSize = <EMAIL>
        this.tickValue = <EMAIL>
        this.trendList = <EMAIL>?.let { ArrayList(it) }
    }
}

/**
 * 弹窗页面状态
 */
sealed class SSDialogUiState {
    data object CategoryLayout : SSDialogUiState()//分类页面
    data object SearchLayout : SSDialogUiState()//搜索页面
}

/**
 * 搜索页的UI状态
 */
sealed class SearchUiState {
    data object FirstEnter : SearchUiState()//首次进入，显示空白页
    data object ResultEmpty : SearchUiState()//搜索结果为空
    data object ResultNotEmpty : SearchUiState()//搜索结果不为空

}